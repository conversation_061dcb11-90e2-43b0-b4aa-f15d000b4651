import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/core.dart';
import 'features/auth/presentation/providers/auth_provider.dart';

/// GymKod Pro Mobile App
///
/// Bu uygulama Angular frontend'deki tasarım sistemini takip eder.
/// Referans: GymProjectFrontend tasarım sistemi
///
/// Ana özellikler:
/// - JWT Token Authentication
/// - QR Code sistemi
/// - Dark/Light theme support
/// - Riverpod state management
/// - Go Router navigation

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // System UI ayarları (Angular'daki global styles'a benzer)
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // Desteklenen orientasyonlar
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Service'leri test et (Debug mode'da)
  await _testServices();

  runApp(
    const ProviderScope(
      child: GymKodProApp(),
    ),
  );
}

/// Service'leri test et (Debug mode'da) - Optimize edildi
Future<void> _testServices() async {
  if (!kDebugMode) return;

  try {
    // Logging Service Test
    LoggingService.info('App başlatılıyor...', tag: 'MAIN');

    // Service'leri paralel olarak test et (performans optimizasyonu)
    final futures = <Future<void>>[
      _testStorageService(),
      _testDeviceService(),
      _testJwtService(),
      _testApiService(),
    ];

    await Future.wait(futures);
    LoggingService.info('Tüm service\'ler başarıyla test edildi', tag: 'MAIN');
  } catch (e, stackTrace) {
    LoggingService.error('Service test hatası', tag: 'MAIN', error: e, stackTrace: stackTrace);
  }
}

/// Storage Service'i test et
Future<void> _testStorageService() async {
  final storageService = StorageService();
  final storageWorking = await storageService.testStorage();
  LoggingService.info('Storage Service: ${storageWorking ? 'Çalışıyor' : 'Hata'}', tag: 'MAIN');
}

/// Device Service'i test et
Future<void> _testDeviceService() async {
  final deviceService = DeviceService();
  final deviceInfo = await deviceService.getDeviceInfo();
  LoggingService.info('Device: ${deviceInfo.deviceType} - ${deviceInfo.deviceName}', tag: 'MAIN');
}

/// JWT Service'i test et
Future<void> _testJwtService() async {
  JwtService();
  LoggingService.info('JWT Service: Hazır', tag: 'MAIN');
}

/// API Service'i test et
Future<void> _testApiService() async {
  ApiService();
  LoggingService.info('API Service: Hazır', tag: 'MAIN');
}

class GymKodProApp extends ConsumerStatefulWidget {
  const GymKodProApp({super.key});

  @override
  ConsumerState<GymKodProApp> createState() => _GymKodProAppState();
}

class _GymKodProAppState extends ConsumerState<GymKodProApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // App lifecycle değişikliklerini logla
    LoggingService.info('App lifecycle changed: $state', tag: 'LIFECYCLE');

    // Auth durumunu kontrol et
    final authState = ref.read(authProvider);
    if (authState.isAuthenticated) {
      switch (state) {
        case AppLifecycleState.resumed:
          // Uygulama foreground'a döndüğünde token durumunu kontrol et
          LoggingService.authLog('App resumed - checking auth status');
          ref.read(authProvider.notifier).checkAuthStatus();
          break;
        case AppLifecycleState.paused:
          // Uygulama background'a geçtiğinde özel bir işlem yapmıyoruz
          // TokenRefreshService background'da çalışmaya devam edecek
          LoggingService.authLog('App paused - token refresh service continues');
          break;
        default:
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);
    final themeMode = ref.watch(themeProvider); // Tema provider'dan tema modunu al

    // Auth state değişikliklerini dinle
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Kullanıcı giriş yaptığında profil fotoğrafını refresh et
      if (previous?.isAuthenticated != next.isAuthenticated && next.isAuthenticated) {
        LoggingService.info('User logged in, refreshing profile image', tag: 'PROFILE_IMAGE');
        ref.read(profileImageProvider.notifier).forceRefreshProfileImage();
      }
    });

    return MaterialApp.router(
      // App Configuration
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Localization Configuration
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('tr', 'TR'), // Türkçe
        Locale('en', 'US'), // İngilizce
      ],
      locale: const Locale('tr', 'TR'), // Varsayılan Türkçe

      // Theme Configuration (Angular frontend'deki theme system)
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode, // Provider'dan gelen tema modu

      // Router Configuration (Go Router)
      routerConfig: router,
    );
  }
}