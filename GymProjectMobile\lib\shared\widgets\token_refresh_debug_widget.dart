/// Token Refresh Debug Widget - GymKod Pro Mobile
///
/// Bu widget TokenRefreshService'in durumunu debug etmek için kullanılır.
/// Sadece debug mode'da görünür.
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/core.dart';

/// Token Refresh Debug Widget
/// Sadece debug mode'da görünür ve token refresh durumunu gösterir
class TokenRefreshDebugWidget extends ConsumerWidget {
  const TokenRefreshDebugWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Release mode'da hiçbir şey gösterme
    if (kReleaseMode) return const SizedBox.shrink();

    final debugInfo = ref.watch(tokenRefreshDebugProvider);
    final status = ref.watch(tokenRefreshStatusProvider);

    return Container(
      margin: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.orange, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(
                Icons.bug_report,
                color: Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Token Refresh Debug',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildDebugRow('Status', status, _getStatusColor(status)),
          _buildDebugRow('Active', debugInfo['isActive'].toString(), 
            debugInfo['isActive'] == true ? Colors.green : Colors.red),
          _buildDebugRow('Refreshing', debugInfo['isRefreshing'].toString(),
            debugInfo['isRefreshing'] == true ? Colors.yellow : Colors.grey),
          _buildDebugRow('Timer Active', debugInfo['hasActiveTimer'].toString(),
            debugInfo['hasActiveTimer'] == true ? Colors.green : Colors.grey),
        ],
      ),
    );
  }

  Widget _buildDebugRow(String label, String value, Color valueColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: valueColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Active (Timer Running)':
        return Colors.green;
      case 'Active (No Timer)':
        return Colors.orange;
      case 'Refreshing...':
        return Colors.yellow;
      case 'Inactive':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// Floating Token Refresh Debug Button
/// Ekranın sağ alt köşesinde floating button olarak gösterir
class FloatingTokenRefreshDebug extends ConsumerStatefulWidget {
  const FloatingTokenRefreshDebug({super.key});

  @override
  ConsumerState<FloatingTokenRefreshDebug> createState() => _FloatingTokenRefreshDebugState();
}

class _FloatingTokenRefreshDebugState extends ConsumerState<FloatingTokenRefreshDebug> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    // Release mode'da hiçbir şey gösterme
    if (kReleaseMode) return const SizedBox.shrink();

    return Positioned(
      bottom: 80,
      right: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (_isExpanded) ...[
            const TokenRefreshDebugWidget(),
            const SizedBox(height: 8),
          ],
          FloatingActionButton.small(
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            backgroundColor: Colors.orange,
            child: Icon(
              _isExpanded ? Icons.close : Icons.bug_report,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}

/// Manual Token Refresh Button
/// Test amaçlı manuel token refresh butonu
class ManualTokenRefreshButton extends ConsumerWidget {
  const ManualTokenRefreshButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Release mode'da hiçbir şey gösterme
    if (kReleaseMode) return const SizedBox.shrink();

    final isRefreshing = ref.watch(isTokenRefreshingProvider);

    return ElevatedButton.icon(
      onPressed: isRefreshing ? null : () async {
        final service = ref.read(tokenRefreshServiceProvider);
        final success = await service.refreshTokenManually();
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success ? 'Token refreshed successfully' : 'Token refresh failed',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      },
      icon: isRefreshing 
        ? const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Icon(Icons.refresh),
      label: Text(isRefreshing ? 'Refreshing...' : 'Manual Refresh'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }
}
