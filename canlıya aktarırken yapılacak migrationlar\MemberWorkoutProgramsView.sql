-- Üye Program Atama Sistemi View Script
-- Bu script üye program atama sisteminin performans view'larını oluşturur
-- vw_MemberWorkoutProgramDetails: Ana detay view (index<PERSON>, hızl<PERSON> eri<PERSON>im)
-- vw_MemberWorkoutProgramStats: İstatistik view (raporlama için)
-- vw_MemberWorkoutProgramSummary: Özet view (mobil API için)

USE [GymProject]
GO

-- 1. ANA DETAY VIEW (Schema bound - indexlenebilir)
-- <PERSON><PERSON><PERSON> <PERSON>ull<PERSON>lan join'ler için cache optimizasyonu
CREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails]
WITH SCHEMABINDING
AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.Name AS MemberName,
    m.PhoneNumber AS MemberPhone,
    m.Email AS MemberEmail,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.Description AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate,
    mwp.UpdatedDate
FROM dbo.MemberWorkoutPrograms mwp
INNER JOIN dbo.Members m ON mwp.MemberID = m.MemberID
INNER JOIN dbo.WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- 2. PERFORMANS İNDEXLERİ (10.000+ kullanıcı için optimizasyon)

-- Ana clustered index (primary key bazlı)
CREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberWorkoutProgramID])
GO

-- Üye bazlı sorgular için (mobil API'de en çok kullanılacak)
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_MemberID]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberID])
INCLUDE ([ProgramName], [StartDate], [EndDate], [ExperienceLevel])
GO

-- Şirket bazlı sorgular için (admin paneli)
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_CompanyID]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([CompanyID])
INCLUDE ([MemberName], [ProgramName], [AssignedDate])
GO

-- Program bazlı sorgular için
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_TemplateID]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([WorkoutProgramTemplateID])
INCLUDE ([MemberName], [AssignedDate])
GO

-- 3. İSTATİSTİK VIEW (Raporlama ve dashboard için)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramStats] AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    mwp.WorkoutProgramTemplateID,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    -- Program gün sayısı
    (SELECT COUNT(*) FROM WorkoutProgramDays wpd
     WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS DayCount,
    -- Program egzersiz sayısı
    (SELECT COUNT(*) FROM WorkoutProgramExercises wpe
     INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
     WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS ExerciseCount,
    -- Aktif program sayısı (üye bazında)
    (SELECT COUNT(*) FROM MemberWorkoutPrograms mwp2
     WHERE mwp2.MemberID = mwp.MemberID AND mwp2.IsActive = 1) AS ActiveProgramCount,
    -- Program süresi (gün olarak)
    CASE
        WHEN mwp.EndDate IS NOT NULL THEN DATEDIFF(DAY, mwp.StartDate, mwp.EndDate)
        ELSE DATEDIFF(DAY, mwp.StartDate, GETDATE())
    END AS ProgramDurationDays
FROM MemberWorkoutPrograms mwp
WHERE mwp.IsActive = 1
GO

-- 4. ÖZET VIEW (Mobil API için hafif versiyon)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramSummary] AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.Name AS MemberName,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.StartDate,
    mwp.EndDate,
    mwp.IsActive,
    -- Basit hesaplamalar
    CASE
        WHEN mwp.EndDate IS NOT NULL AND mwp.EndDate < GETDATE() THEN 'Tamamlandı'
        WHEN mwp.StartDate > GETDATE() THEN 'Başlamadı'
        ELSE 'Devam Ediyor'
    END AS ProgramStatus
FROM MemberWorkoutPrograms mwp
INNER JOIN Members m ON mwp.MemberID = m.MemberID
INNER JOIN WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

PRINT 'Üye Program Atama View sistemi migration tamamlandı!'
PRINT 'Oluşturulan view''lar:'
PRINT '- vw_MemberWorkoutProgramDetails (Schema bound - indexli ana view)'
PRINT '- vw_MemberWorkoutProgramStats (İstatistik ve raporlama view)'
PRINT '- vw_MemberWorkoutProgramSummary (Mobil API için özet view)'
PRINT 'Performans indexleri 10.000+ kullanıcı için optimize edildi.'
PRINT 'Multi-tenant mimarisi destekleniyor.'
GO
