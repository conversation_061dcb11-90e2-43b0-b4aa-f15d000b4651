/// Auth Provider - GymKod Pro Mobile
///
/// Bu provider Riverpod ile auth state management'ı yapar.
/// Referans: Angular frontend'deki auth service state management
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/models.dart';
import '../../../../core/services/services.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';

/// Auth State
class AuthState {
  final bool isAuthenticated;
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isInitialized;

  /// <PERSON><PERSON>re <PERSON>ğ<PERSON>ştirme zorunluluğu (Angular frontend pattern)
  final bool requirePasswordChange;

  const AuthState({
    this.isAuthenticated = false,
    this.user,
    this.isLoading = false,
    this.error,
    this.isInitialized = false,
    this.requirePasswordChange = false,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isInitialized,
    bool? requirePasswordChange,
    bool clearUser = false,
    bool clearError = false,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: clearUser ? null : (user ?? this.user),
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      isInitialized: isInitialized ?? this.isInitialized,
      requirePasswordChange: requirePasswordChange ?? this.requirePasswordChange,
    );
  }

  @override
  String toString() {
    return 'AuthState(isAuthenticated: $isAuthenticated, user: ${user?.name}, isLoading: $isLoading, error: $error, isInitialized: $isInitialized, requirePasswordChange: $requirePasswordChange)';
  }
}

/// Auth Repository Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl();
});

/// Auth State Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final DeviceService _deviceService;
  final StorageService _storageService;
  final TokenRefreshService _tokenRefreshService;

  AuthNotifier(this._authRepository, this._deviceService, this._storageService, this._tokenRefreshService) : super(const AuthState()) {
    _initialize();
  }

  /// Auth state'i initialize et - Optimize edildi
  Future<void> _initialize() async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Initializing');

      state = state.copyWith(isLoading: true);

      // Auth durumunu ve user bilgilerini paralel olarak al (performans optimizasyonu)
      final futures = await Future.wait([
        _authRepository.isAuthenticated(),
        _authRepository.getCurrentUser(),
        _storageService.getRequirePasswordChange(),
      ]);

      final isAuthenticated = futures[0] as bool;
      final user = futures[1] as UserModel?;
      final requirePasswordChange = futures[2] as bool;

      if (isAuthenticated && user != null) {
        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          isInitialized: true,
          requirePasswordChange: requirePasswordChange,
          clearError: true,
        );

        // Token refresh service'i başlat (Angular pattern)
        await _startTokenRefreshService();

        LoggingService.stateLog('AuthNotifier', 'Initialized as authenticated',
          state: 'User: ${user.name}, RequirePasswordChange: $requirePasswordChange');
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          isInitialized: true,
          clearUser: true,
          clearError: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Initialized as not authenticated');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Initialize');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        isInitialized: true,
        error: 'Başlatma sırasında hata oluştu',
        clearUser: true,
      );
    }
  }

  /// Kullanıcı girişi
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Login attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al
      final deviceInfo = await _deviceService.getDeviceInfoString();

      // Login API çağrısı
      final result = await _authRepository.login(
        email: email,
        password: password,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        // Şifre değiştirme zorunluluğunu kontrol et (Angular frontend pattern)
        final requirePasswordChange = result.extraData?['requirePasswordChange'] == true;

        // Debug log
        LoggingService.stateLog('AuthNotifier', 'Login result analysis',
          state: 'ExtraData: ${result.extraData}, RequirePasswordChange: $requirePasswordChange, Message: ${result.message}');

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          requirePasswordChange: requirePasswordChange,
          clearError: true,
        );

        // Token refresh service'i başlat (Angular pattern)
        await _startTokenRefreshService();

        LoggingService.stateLog('AuthNotifier', 'Login successful', state: 'User: ${user?.name}, RequirePasswordChange: $requirePasswordChange');
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Login failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Login');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Giriş işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Üye kaydı
  Future<bool> registerMember({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String phoneNumber,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Member register attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al
      final deviceInfo = await _deviceService.getDeviceInfoString();

      // Register API çağrısı
      final result = await _authRepository.registerMember(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        phoneNumber: phoneNumber,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          clearError: true,
        );

        // Token refresh service'i başlat (Angular pattern)
        await _startTokenRefreshService();

        LoggingService.stateLog('AuthNotifier', 'Member register successful', state: user?.name);
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Member register failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Member Register');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Kayıt işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Admin/Owner kaydı
  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Admin register attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al
      final deviceInfo = await _deviceService.getDeviceInfoString();

      // Register API çağrısı
      final result = await _authRepository.register(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          clearError: true,
        );

        // Token refresh service'i başlat (Angular pattern)
        await _startTokenRefreshService();

        LoggingService.stateLog('AuthNotifier', 'Admin register successful', state: user?.name);
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Admin register failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Admin Register');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Kayıt işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Çıkış yapma
  Future<void> logout() async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Logout');

      state = state.copyWith(isLoading: true);

      // Repository'den logout
      await _authRepository.logout();

      // Token refresh service'i durdur (Angular pattern)
      _stopTokenRefreshService();

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        clearUser: true,
        clearError: true,
      );

      LoggingService.stateLog('AuthNotifier', 'Logout successful');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Logout');

      // Hata olsa bile logout yap
      _stopTokenRefreshService();

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        clearUser: true,
        clearError: true,
      );
    }
  }

  /// Profil bilgilerini yenile
  Future<void> refreshProfile() async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Refresh profile');

      if (!state.isAuthenticated) return;

      final result = await _authRepository.getProfile();

      if (result.isSuccess && result.data != null) {
        state = state.copyWith(
          user: result.data,
          clearError: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Profile refreshed', state: result.data?.name);
      } else {
        LoggingService.stateLog('AuthNotifier', 'Profile refresh failed', state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Refresh Profile');
    }
  }

  /// Şifre değiştirme (Angular frontend pattern)
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Change password attempt');

      state = state.copyWith(isLoading: true, clearError: true);

      // Change password API çağrısı
      final result = await _authRepository.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.isSuccess) {
        // Şifre değiştirme zorunluluğunu kaldır
        state = state.copyWith(
          isLoading: false,
          requirePasswordChange: false,
          clearError: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Password changed successfully');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );

        LoggingService.stateLog('AuthNotifier', 'Password change failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Change Password');

      state = state.copyWith(
        isLoading: false,
        error: 'Şifre değiştirme işlemi sırasında hata oluştu',
      );

      return false;
    }
  }

  /// Error'ı temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Auth durumunu kontrol et
  Future<void> checkAuthStatus() async {
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated != state.isAuthenticated) {
        if (isAuthenticated) {
          final user = await _authRepository.getCurrentUser();
          state = state.copyWith(
            isAuthenticated: true,
            user: user,
          );
          // Token refresh service'i başlat
          await _startTokenRefreshService();
        } else {
          state = state.copyWith(
            isAuthenticated: false,
            clearUser: true,
          );
          // Token refresh service'i durdur
          _stopTokenRefreshService();
        }
      }
    } catch (e) {
      LoggingService.stateLog('AuthNotifier', 'Auth status check error', state: e.toString());
    }
  }

  /// Token refresh service'i başlat (Angular: startRefreshTokenTimer)
  Future<void> _startTokenRefreshService() async {
    try {
      LoggingService.authLog('Starting token refresh service');

      await _tokenRefreshService.initialize(
        onTokenRefreshed: () {
          LoggingService.authLog('Token refreshed by service - updating user data');
          // Token yenilendiğinde user data'yı güncelle
          _refreshUserDataFromToken();
        },
        onRefreshFailed: () {
          LoggingService.authLog('Token refresh failed - logging out user');
          // Token refresh başarısızsa kullanıcıyı logout et
          logout();
        },
      );

      LoggingService.authLog('Token refresh service started successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _startTokenRefreshService');
    }
  }

  /// Token refresh service'i durdur (Angular: stopRefreshTokenTimer)
  void _stopTokenRefreshService() {
    try {
      LoggingService.authLog('Stopping token refresh service');
      _tokenRefreshService.stop();
      LoggingService.authLog('Token refresh service stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _stopTokenRefreshService');
    }
  }

  /// Token'dan user data'yı yenile
  Future<void> _refreshUserDataFromToken() async {
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        state = state.copyWith(user: user);
        LoggingService.authLog('User data refreshed from token', details: user.name);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _refreshUserDataFromToken');
    }
  }
}

/// Auth State Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  final deviceService = DeviceService();
  final storageService = StorageService();
  final tokenRefreshService = TokenRefreshService();
  return AuthNotifier(authRepository, deviceService, storageService, tokenRefreshService);
});

/// Auth State Getters
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});

final authInitializedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isInitialized;
});

final requirePasswordChangeProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).requirePasswordChange;
});
