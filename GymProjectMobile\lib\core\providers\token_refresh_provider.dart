/// Token Refresh Provider - GymKod Pro Mobile
///
/// Bu provider TokenRefreshService'in durumunu takip eder ve debug bilgileri sağlar.
/// Angular frontend'deki token refresh monitoring'e benzer.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/services.dart';

/// Token Refresh Service Provider
final tokenRefreshServiceProvider = Provider<TokenRefreshService>((ref) {
  return TokenRefreshService();
});

/// Token Refresh Debug Info Provider
final tokenRefreshDebugProvider = Provider<Map<String, dynamic>>((ref) {
  final service = ref.watch(tokenRefreshServiceProvider);
  return service.getDebugInfo();
});

/// Token Refresh Status Provider
final tokenRefreshStatusProvider = Provider<String>((ref) {
  final debugInfo = ref.watch(tokenRefreshDebugProvider);
  
  if (debugInfo['isActive'] == true) {
    if (debugInfo['isRefreshing'] == true) {
      return 'Refreshing...';
    } else if (debugInfo['hasActiveTimer'] == true) {
      return 'Active (Timer Running)';
    } else {
      return 'Active (No Timer)';
    }
  } else {
    return 'Inactive';
  }
});

/// Token Refresh Service'in aktif olup olmadığını kontrol eden provider
final isTokenRefreshActiveProvider = Provider<bool>((ref) {
  final service = ref.watch(tokenRefreshServiceProvider);
  return service.isActive;
});

/// Token Refresh Service'in refresh yapıp yapmadığını kontrol eden provider
final isTokenRefreshingProvider = Provider<bool>((ref) {
  final service = ref.watch(tokenRefreshServiceProvider);
  return service.isRefreshing;
});
