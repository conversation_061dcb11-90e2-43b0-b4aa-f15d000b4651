﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserCompanyManager : IUserCompanyService
    {
        IUserCompanyDal _userCompanyDal;

        public UserCompanyManager(IUserCompanyDal userCompanyDal)
        {
            _userCompanyDal = userCompanyDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Add(UserCompany userCompany)
        {
            _userCompanyDal.Add(userCompany);
            return new SuccessResult(Messages.UserCompanyAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Delete(int id)
        {
            _userCompanyDal.Delete(id);
            return new SuccessResult(Messages.UserCompanyDeleted);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 30, "UserCompany", "All")]
        public IDataResult<List<UserCompany>> GetAll()
        {
            return new SuccessDataResult<List<UserCompany>>(_userCompanyDal.GetAll());
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Update(UserCompany userCompany)
        {
            _userCompanyDal.Update(userCompany);
            return new SuccessResult(Messages.UserCompanyUpdated);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "UserCompany", "Details")]
        public IDataResult<List<UserCompanyDetailDto>> GetUserCompanyDetails()
        {
            return new SuccessDataResult<List<UserCompanyDetailDto>>(_userCompanyDal.GetUserCompanyDetails());
        }

        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "UserCompany", "ByUserId")]
        public IDataResult<int> GetUserCompanyId(int userId)
        {
            int companyId = _userCompanyDal.GetUserCompanyId(userId);
            return new SuccessDataResult<int>(companyId);
        }

        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "UserCompany", "UserCompanies")]
        public IDataResult<List<UserCompany>> GetUserCompanies(int userId)
        {
            var userCompanies = _userCompanyDal.GetAll(uc => uc.UserID == userId && uc.IsActive == true);
            if (userCompanies == null || userCompanies.Count == 0)
            {
                return new ErrorDataResult<List<UserCompany>>(Messages.UserCompanyNotFound);
            }
            return new SuccessDataResult<List<UserCompany>>(userCompanies);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult UpdateActiveCompany(int userId, int companyId)
        {
            // Kullanıcının tüm şirketlerini getir
            var userCompanies = _userCompanyDal.GetAll(uc => uc.UserID == userId);

            // Belirtilen şirketin kullanıcıya ait olup olmadığını kontrol et
            var targetCompany = userCompanies.FirstOrDefault(uc => uc.CompanyId == companyId);
            if (targetCompany == null)
            {
                return new ErrorResult(Messages.UserCompanyNotFound);
            }

            // Şirketin aktif olup olmadığını kontrol et
            if (targetCompany.IsActive != true)
            {
                return new ErrorResult(Messages.UserCompanyNotActive);
            }

            return new SuccessResult(Messages.UserCompanyUpdated);
        }
    }
}
